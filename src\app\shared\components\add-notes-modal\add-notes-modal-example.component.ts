import { Component } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AddNotesModalService } from './add-notes-modal.service';
import type { AddNotesResult } from './add-notes-modal.component';
import { TransactionType } from '../../models/enums';

@Component({
  selector: 'app-add-notes-modal-example',
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ],
  template: `
    <div class="example-container">
      <h3>Add Notes Modal Examples</h3>
      
      <div class="button-group">
        <button mat-raised-button color="primary" (click)="openBasicModal()">
          <mat-icon>note_add</mat-icon>
          Open Basic Modal
        </button>
        
        <button mat-raised-button color="accent" (click)="openModalForSeries()">
          <mat-icon>category</mat-icon>
          Open for Mandela Series
        </button>
        
        <button mat-raised-button color="warn" (click)="openModalForSeriesAndDenomination()">
          <mat-icon>monetization_on</mat-icon>
          Open for Mandela R100
        </button>

        <button mat-raised-button (click)="openQuickAddModal()">
          <mat-icon>flash_on</mat-icon>
          Quick Add Notes
        </button>
      </div>
    </div>
  `,
  styles: [`
    .example-container {
      padding: 20px;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .button-group {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-top: 20px;
    }
    
    button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class AddNotesModalExampleComponent {

  constructor(
    private addNotesModalService: AddNotesModalService,
    private snackBar: MatSnackBar
  ) {}

  openBasicModal(): void {
    this.addNotesModalService.openAddNotesModal()
      .subscribe(result => {
        if (result) {
          this.handleResult(result);
        }
      });
  }

  openModalForSeries(): void {
    this.addNotesModalService.openAddNotesModalForSeries('MANDELA', 'Add Mandela Series Notes')
      .subscribe(result => {
        if (result) {
          this.handleResult(result);
        }
      });
  }

  openModalForSeriesAndDenomination(): void {
    this.addNotesModalService.openAddNotesModalForSeriesAndDenomination(
      'MANDELA', 
      100, 
      'Add Mandela R100 Notes'
    ).subscribe(result => {
      if (result) {
        this.handleResult(result);
      }
    });
  }

  openQuickAddModal(): void {
    this.addNotesModalService.openQuickAddNotesModal()
      .subscribe(result => {
        if (result) {
          this.handleResult(result);
        }
      });
  }

  private handleResult(result: AddNotesResult): void {
    const action = result.transactionType === TransactionType.Addition ? 'Added' : 'Removed';
    const totalValue = result.denomination * result.quantity;
    
    const message = `${action} ${result.quantity} x R${result.denomination} ${result.seriesName} notes (Total: R${totalValue})`;
    
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'top'
    });

    console.log('Add Notes Result:', result);
  }
}
