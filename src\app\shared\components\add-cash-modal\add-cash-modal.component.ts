import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { CommonModule } from '@angular/common';
import { CashType } from '../../models/cashtype.model';
import { TransactionType } from '../../models/enums';

export interface AddCashDialogData {
  title?: string;
  cashTypes?: CashType[];
}

export interface AddCashResult {
  cashTypeId: string;
  denomination: number;
  quantity: number;
  transactionType: TransactionType;
  notes?: string;
}

@Component({
  selector: 'app-add-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule
  ],
  templateUrl: './add-cash-modal.component.html',
  styleUrls: ['./add-cash-modal.component.scss']
})
export class AddCashModalComponent implements OnInit {
  addCashForm: FormGroup;
  transactionTypes = Object.values(TransactionType);

  // Common denominations for cash
  denominations = [
    { value: 10, label: 'R10' },
    { value: 20, label: 'R20' },
    { value: 50, label: 'R50' },
    { value: 100, label: 'R100' },
    { value: 200, label: 'R200' }
  ];

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<AddCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCashDialogData = {}
  ) {
    this.addCashForm = this.fb.group({
      cashTypeId: ['', Validators.required],
      denomination: ['', [Validators.required, Validators.min(1)]],
      quantity: ['', [Validators.required, Validators.min(1)]],
      transactionType: [TransactionType.Addition, Validators.required],
      notes: ['']
    });
  }

  ngOnInit(): void {
    // Set default values if needed
    if (this.data.cashTypes && this.data.cashTypes.length === 1) {
      this.addCashForm.patchValue({
        cashTypeId: this.data.cashTypes[0].id
      });
    }
  }

  onSubmit(): void {
    if (this.addCashForm.valid) {
      const result: AddCashResult = this.addCashForm.value;
      this.dialogRef.close(result);
    } else {
      // Mark all fields as touched to show validation errors
      this.addCashForm.markAllAsTouched();
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  getTotalValue(): number {
    const denomination = this.addCashForm.get('denomination')?.value || 0;
    const quantity = this.addCashForm.get('quantity')?.value || 0;
    return denomination * quantity;
  }

  getFieldError(fieldName: string): string {
    const field = this.addCashForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${this.getFieldLabel(fieldName)} is required`;
      }
      if (field.errors['min']) {
        return `${this.getFieldLabel(fieldName)} must be greater than 0`;
      }
    }
    return '';
  }

  private getFieldLabel(fieldName: string): string {
    const labels: { [key: string]: string } = {
      cashTypeId: 'Cash Type',
      denomination: 'Denomination',
      quantity: 'Quantity',
      transactionType: 'Transaction Type'
    };
    return labels[fieldName] || fieldName;
  }
}
