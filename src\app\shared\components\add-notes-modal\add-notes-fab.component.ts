import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { AddNotesModalService } from './add-notes-modal.service';
import type { AddNotesResult } from './add-notes-modal.component';
import { TransactionType } from '../../models/enums';

@Component({
  selector: 'app-add-notes-fab',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatSnackBarModule
  ],
  template: `
    <button 
      mat-fab 
      [color]="color"
      [class]="'add-notes-fab ' + position"
      (click)="openAddNotesModal()"
      [matTooltip]="tooltipText"
      matTooltipPosition="left">
      <mat-icon>{{ icon }}</mat-icon>
    </button>
  `,
  styles: [`
    .add-notes-fab {
      position: fixed;
      z-index: 1000;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
      }

      &:active {
        transform: scale(0.95);
      }

      // Position variants
      &.bottom-right {
        bottom: 24px;
        right: 24px;
      }

      &.bottom-left {
        bottom: 24px;
        left: 24px;
      }

      &.top-right {
        top: 24px;
        right: 24px;
      }

      &.top-left {
        top: 24px;
        left: 24px;
      }

      // Custom gradient background for primary color
      &.mat-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        
        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
      }
    }

    // Animation for entrance
    @keyframes fabSlideIn {
      from {
        opacity: 0;
        transform: scale(0) rotate(-180deg);
      }
      to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
      }
    }

    .add-notes-fab {
      animation: fabSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // Responsive adjustments
    @media (max-width: 768px) {
      .add-notes-fab {
        &.bottom-right {
          bottom: 16px;
          right: 16px;
        }

        &.bottom-left {
          bottom: 16px;
          left: 16px;
        }

        &.top-right {
          top: 16px;
          right: 16px;
        }

        &.top-left {
          top: 16px;
          left: 16px;
        }
      }
    }
  `]
})
export class AddNotesFabComponent {
  @Input() color: 'primary' | 'accent' | 'warn' = 'primary';
  @Input() icon: string = 'note_add';
  @Input() tooltipText: string = 'Add Notes';
  @Input() position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' = 'bottom-right';
  @Input() preSelectedSeries?: string;
  @Input() preSelectedDenomination?: number;

  constructor(
    private addNotesModalService: AddNotesModalService,
    private snackBar: MatSnackBar
  ) {}

  openAddNotesModal(): void {
    // Determine which modal method to use based on inputs
    let modalObservable;

    if (this.preSelectedSeries && this.preSelectedDenomination) {
      modalObservable = this.addNotesModalService.openAddNotesModalForSeriesAndDenomination(
        this.preSelectedSeries,
        this.preSelectedDenomination
      );
    } else if (this.preSelectedSeries) {
      modalObservable = this.addNotesModalService.openAddNotesModalForSeries(
        this.preSelectedSeries
      );
    } else {
      modalObservable = this.addNotesModalService.openAddNotesModal();
    }

    modalObservable.subscribe(result => {
      if (result) {
        this.handleResult(result);
      }
    });
  }

  private handleResult(result: AddNotesResult): void {
    const action = result.transactionType === TransactionType.Addition ? 'Added' : 'Removed';
    const totalValue = result.denomination * result.quantity;
    
    const message = `${action} ${result.quantity} x R${result.denomination} ${result.seriesName} notes (Total: R${totalValue})`;
    
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'top',
      panelClass: ['success-snackbar']
    });

    console.log('Add Notes Result:', result);
  }
}
