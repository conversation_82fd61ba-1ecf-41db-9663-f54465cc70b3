import { Component, Inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { TransactionType } from '../../models/enums';

// Data interface for the dialog
export interface AddNotesDialogData {
  title?: string;
  preSelectedSeries?: string;
  preSelectedDenomination?: number;
}

// Result interface for the dialog
export interface AddNotesResult {
  series: string;
  seriesName: string;
  denomination: number;
  quantity: number;
  transactionType: TransactionType;
  notes?: string;
  cashTypeId: number;
}

// Series configuration
interface NoteSeries {
  id: string;
  name: string;
  denominations: number[];
  color: string;
}

@Component({
  selector: 'app-add-notes-modal',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule
  ],
  templateUrl: './add-notes-modal.component.html',
  styleUrls: ['./add-notes-modal.component.scss']
})
export class AddNotesModalComponent implements OnInit {
  addNotesForm: FormGroup;
  transactionTypes = Object.values(TransactionType);

  // Note series configuration
  noteSeries: NoteSeries[] = [
    {
      id: 'MANDELA',
      name: 'Mandela Series',
      denominations: [10, 20, 50, 100, 200],
      color: '#4CAF50'
    },
    {
      id: 'BIG_5',
      name: 'Big 5 Series',
      denominations: [10, 20, 50, 100, 200],
      color: '#FF9800'
    },
    {
      id: 'COMMEMORATIVE',
      name: 'Commemorative Series',
      denominations: [10, 20, 50, 100, 200],
      color: '#9C27B0'
    },
    {
      id: 'V6',
      name: 'V6 Series',
      denominations: [10, 20, 50, 100, 200],
      color: '#2196F3'
    }
  ];

  // Cash type mapping (based on db.json structure)
  private cashTypeMapping: { [key: string]: { [denomination: number]: number } } = {
    'MANDELA': { 10: 1, 20: 2, 50: 3, 100: 4, 200: 5 },
    'V6': { 10: 6, 20: 7, 50: 8, 100: 9, 200: 10 },
    'BIG_5': { 10: 11, 20: 12, 50: 13, 100: 14, 200: 15 },
    'COMMEMORATIVE': { 10: 16, 20: 17, 50: 18, 100: 19, 200: 20 }
  };

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<AddNotesModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddNotesDialogData = {}
  ) {
    this.addNotesForm = this.fb.group({
      series: ['', Validators.required],
      denomination: ['', Validators.required],
      quantity: ['', [Validators.required, Validators.min(1)]],
      transactionType: [TransactionType.Addition, Validators.required],
      notes: ['']
    });
  }

  ngOnInit(): void {
    // Set pre-selected values if provided
    if (this.data.preSelectedSeries) {
      this.addNotesForm.patchValue({ series: this.data.preSelectedSeries });
    }
    if (this.data.preSelectedDenomination) {
      this.addNotesForm.patchValue({ denomination: this.data.preSelectedDenomination });
    }

    // Watch for series changes to update available denominations
    this.addNotesForm.get('series')?.valueChanges.subscribe(() => {
      this.addNotesForm.patchValue({ denomination: '' });
    });
  }

  getSelectedSeries(): NoteSeries | undefined {
    const seriesId = this.addNotesForm.get('series')?.value;
    return this.noteSeries.find(series => series.id === seriesId);
  }

  getAvailableDenominations(): number[] {
    const selectedSeries = this.getSelectedSeries();
    return selectedSeries ? selectedSeries.denominations : [];
  }

  getTotalValue(): number {
    const denomination = this.addNotesForm.get('denomination')?.value || 0;
    const quantity = this.addNotesForm.get('quantity')?.value || 0;
    return denomination * quantity;
  }

  getFieldError(fieldName: string): string | null {
    const field = this.addNotesForm.get(fieldName);
    if (field && field.invalid && (field.dirty || field.touched)) {
      if (field.errors?.['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors?.['min']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['min'].min}`;
      }
    }
    return null;
  }

  onSubmit(): void {
    if (this.addNotesForm.valid) {
      const formValue = this.addNotesForm.value;
      const selectedSeries = this.getSelectedSeries();
      
      if (!selectedSeries) {
        return;
      }

      // Get the cash type ID from the mapping
      const cashTypeId = this.cashTypeMapping[formValue.series]?.[formValue.denomination];
      
      if (!cashTypeId) {
        console.error('Could not find cash type ID for series and denomination');
        return;
      }

      const result: AddNotesResult = {
        series: formValue.series,
        seriesName: selectedSeries.name,
        denomination: formValue.denomination,
        quantity: formValue.quantity,
        transactionType: formValue.transactionType,
        notes: formValue.notes,
        cashTypeId: cashTypeId
      };

      this.dialogRef.close(result);
    }
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
