import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';

// Angular Material Imports
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

// Shared Components
import { AddCashModalService } from '../../../shared/components/add-cash-modal/add-cash-modal.service';
import type { AddCashResult, AddCashDialogData } from '../../../shared/components/add-cash-modal/add-cash-modal.component';

import type { CashType } from '../../../shared/models/cashtype.model';

@Component({
  selector: 'app-inventory-overview',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressBarModule,
    MatTabsModule,
    MatSnackBarModule,
    MatTooltipModule
  ],
  templateUrl: './inventory-overview.component.html',
  styleUrls: ['./inventory-overview.component.scss']
})
export class InventoryOverviewComponent implements OnInit {
  // Mock data for demonstration
  inventorySummary = {
    totalValue: 1250000,
    totalNotes: 12500,
    lowStockAlerts: [
      { inventoryId: '1', severity: 'high' as const, message: 'R200 notes running low' }
    ]
  };

  // Mock inventory data - will be redesigned
  inventoryData: any[] = [];

  // Series data for detailed inventory
  seriesData = [
    {
      id: 'mandela',
      name: 'Mandela Series',
      totalBatches: 122,
      totalSingles: 77,
      totalValue: 854040.00,
      denominations: [
        {
          value: 10,
          batches: 20,
          singles: 87,
          totalValue: 20870.00,
          stockLevel: 100
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 30,
          singles: 79,
          totalValue: 153950.00,
          stockLevel: 100
        },
        {
          value: 100,
          batches: 32,
          singles: 61,
          totalValue: 65220.00,
          stockLevel: 100
        },
        {
          value: 200,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        }
      ]
    },
    {
      id: 'big5',
      name: 'Big 5 Series',
      totalBatches: 95,
      totalSingles: 43,
      totalValue: 720500.00,
      denominations: [
        {
          value: 10,
          batches: 5,
          singles: 12,
          totalValue: 512.00,
          stockLevel: 25
        },
        {
          value: 20,
          batches: 25,
          singles: 43,
          totalValue: 50860.00,
          stockLevel: 85
        },
        {
          value: 50,
          batches: 8,
          singles: 15,
          totalValue: 4015.00,
          stockLevel: 35
        },
        {
          value: 100,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 200,
          batches: 18,
          singles: 22,
          totalValue: 362200.00,
          stockLevel: 45
        }
      ]
    },
    {
      id: 'commemorative',
      name: 'Commemorative Series',
      totalBatches: 45,
      totalSingles: 23,
      totalValue: 125000.00,
      denominations: [
        {
          value: 10,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 20,
          batches: 3,
          singles: 8,
          totalValue: 608.00,
          stockLevel: 15
        },
        {
          value: 50,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 100,
          batches: 12,
          singles: 8,
          totalValue: 120800.00,
          stockLevel: 75
        },
        {
          value: 200,
          batches: 2,
          singles: 5,
          totalValue: 4005.00,
          stockLevel: 8
        }
      ]
    },
    {
      id: 'vb',
      name: 'VB Series',
      totalBatches: 67,
      totalSingles: 34,
      totalValue: 890000.00,
      denominations: [
        {
          value: 10,
          batches: 15,
          singles: 45,
          totalValue: 1545.00,
          stockLevel: 65
        },
        {
          value: 20,
          batches: 0,
          singles: 0,
          totalValue: 0.00,
          stockLevel: 0
        },
        {
          value: 50,
          batches: 12,
          singles: 33,
          totalValue: 6033.00,
          stockLevel: 55
        },
        {
          value: 100,
          batches: 22,
          singles: 22,
          totalValue: 222200.00,
          stockLevel: 90
        },
        {
          value: 200,
          batches: 15,
          singles: 12,
          totalValue: 302400.00,
          stockLevel: 60
        }
      ]
    }
  ];

  DENOMINATION_LABELS: { [key: number]: string } = {
    10: 'R10',
    20: 'R20',
    50: 'R50',
    100: 'R100',
    200: 'R200'
  };

  // Mock user service for demo
  userService = {
    hasManagerPrivileges: () => true
  };

  constructor(
    private addCashModalService: AddCashModalService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Component initialization
  }

  // Tab change handler
  onTabChange(event: any): void {
    console.log('Tab changed to:', event.index);
  }

  // Track by function for denomination cards
  trackByDenomination(index: number, denomination: any): any {
    return denomination.value;
  }

  // Get status class based on stock level
  getStatusClass(stockLevel: number): string {
    if (stockLevel === 0) return 'status-out-of-stock';
    if (stockLevel >= 80) return 'status-good';
    if (stockLevel >= 50) return 'status-medium';
    return 'status-low';
  }

  // Get status text based on stock level
  getStatusText(stockLevel: number): string {
    if (stockLevel === 0) return 'Out of Stock';
    if (stockLevel >= 80) return 'In Stock';
    if (stockLevel >= 50) return 'Medium';
    return 'Low Stock';
  }

  // Get cash types for specific series/denomination
  private getCashTypesForSeries(seriesId?: string, denominationValue?: number): CashType[] {
    // Mock cash types - in real app, this would come from a service
    const allCashTypes: CashType[] = [
      { id: '1', name: 'R10 - Mandela Series', currency: 'ZAR', description: 'R10 notes from Mandela Series', isActive: true },
      { id: '2', name: 'R20 - Big 5 Series', currency: 'ZAR', description: 'R20 notes from Big 5 Series', isActive: true },
      { id: '3', name: 'R50 - Mandela Series', currency: 'ZAR', description: 'R50 notes from Mandela Series', isActive: true },
      { id: '4', name: 'R100 - Mandela Series', currency: 'ZAR', description: 'R100 notes from Mandela Series', isActive: true },
      { id: '5', name: 'R200 - Big 5 Series', currency: 'ZAR', description: 'R200 notes from Big 5 Series', isActive: true }
    ];

    if (seriesId && denominationValue) {
      const seriesName = this.seriesData.find(s => s.id === seriesId)?.name;
      return allCashTypes.filter(ct =>
        ct.name?.includes(`R${denominationValue}`) &&
        ct.name?.includes(seriesName || '')
      );
    }

    return allCashTypes;
  }



  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-ZA').format(num);
  }

  getDenominationLabel(denomination: number): string {
    return this.DENOMINATION_LABELS[denomination] || `R${denomination}`;
  }

  formatQuantityDisplay(quantity: number): string {
    const batches = Math.floor(quantity / 100);
    const singles = quantity % 100;

    if (batches === 0) {
      return `${singles} single${singles !== 1 ? 's' : ''}`;
    } else if (singles === 0) {
      return `${batches} batch${batches !== 1 ? 'es' : ''}`;
    } else {
      return `${batches} batch${batches !== 1 ? 'es' : ''} + ${singles} single${singles !== 1 ? 's' : ''}`;
    }
  }



  getStockStatus(item: any): { status: string; class: string } {
    if (item.quantity < 100) {
      return { status: 'Low Stock', class: 'low-stock' };
    } else if (item.quantity > 500) {
      return { status: 'High Stock', class: 'high-stock' };
    }
    return { status: 'Normal', class: 'normal-stock' };
  }

  getStockStatusIcon(item: any): string {
    const status = this.getStockStatus(item);
    if (status.class === 'low-stock') return 'warning';
    if (status.class === 'high-stock') return 'trending_up';
    return 'check_circle';
  }

  getStockPercentage(item: any): number {
    const maxStock = 1000; // Assume max stock is 1000
    return Math.min((item.quantity / maxStock) * 100, 100);
  }

  getStockProgressColor(item: any): string {
    const percentage = this.getStockPercentage(item);
    if (percentage < 20) return 'warn';
    if (percentage > 80) return 'accent';
    return 'primary';
  }

  getSeriesStyleClass(series: string): string {
    return `series-${series.toLowerCase()}`;
  }

  getDenominationImage(denomination: number): string {
    return `assets/images/Money/R${denomination}.jpg`;
  }

  onImageError(event: any): void {
    event.target.style.display = 'none';
  }

  onAddCash(seriesId?: string, denominationValue?: number): void {
    // Configure modal data
    const modalData: AddCashDialogData = {
      title: seriesId && denominationValue
        ? `Add R${denominationValue} Notes`
        : 'Add Cash to Inventory',
      cashTypes: this.getCashTypesForSeries(seriesId, denominationValue)
    };

    // Open the modal
    this.addCashModalService.openAddCashModal(modalData)
      .subscribe(result => {
        if (result) {
          this.handleAddCashResult(result);
        }
      });
  }

  private handleAddCashResult(result: AddCashResult): void {
    const action = result.transactionType === 'Addition' ? 'Added' : 'Removed';
    const totalValue = result.denomination * result.quantity;

    // Show success message
    this.snackBar.open(
      `${action} ${result.quantity} x R${result.denomination} notes (Total: R${totalValue.toLocaleString()})`,
      'Close',
      {
        duration: 5000,
        horizontalPosition: 'center',
        verticalPosition: 'top',
        panelClass: ['success-snackbar']
      }
    );

    // Log the transaction for debugging
    console.log('Cash transaction completed:', {
      result,
      timestamp: new Date().toISOString()
    });

    // Here you would typically call a service to update the backend
    // this.inventoryService.updateCashInventory(result);

    // For now, we'll just refresh the component data
    this.refreshInventoryData();
  }

  private refreshInventoryData(): void {
    // In a real application, you would reload data from the backend
    // For now, we'll just log that a refresh would happen
    console.log('Inventory data would be refreshed from backend');
  }

  // Methods will be redesigned from scratch

  trackByItemId(index: number, item: any): string {
    return item.id || index.toString();
  }

  viewDetails(item: any): void {
    // Navigate to detailed view or open modal
    console.log('View details for item:', item);
    this.snackBar.open(
      `Viewing details for ${this.getDenominationLabel(item.denomination)}`,
      'Close',
      { duration: 2000 }
    );
  }
}
