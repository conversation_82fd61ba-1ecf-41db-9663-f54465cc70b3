# Add Notes Modal Component

A modern, interactive Angular modal component for adding notes to the FFA CMS application with series and denomination selection.

## Features

- **Interactive Series Selection**: Choose from Mandela, Big 5, Commemorative, and V6 series
- **Dynamic Denomination Selection**: Denominations update based on selected series
- **Reactive Forms**: Built with Angular Reactive Forms for robust validation
- **Material Design**: Uses Angular Material components for consistent UI
- **Real-time Calculations**: Shows total value as user inputs denomination and quantity
- **Transaction Types**: Support for both addition and removal of notes
- **Floating Action Button**: Includes a customizable FAB component
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: Follows Angular Material accessibility guidelines

## Usage

### Basic Usage

```typescript
import { AddNotesModalService } from '@shared/components';

export class YourComponent {
  constructor(private addNotesModalService: AddNotesModalService) {}

  openAddNotesModal() {
    this.addNotesModalService.openAddNotesModal()
      .subscribe(result => {
        if (result) {
          this.handleNotesTransaction(result);
        }
      });
  }

  private handleNotesTransaction(result: AddNotesResult) {
    console.log('Notes transaction:', result);
    // Implement your business logic here
  }
}
```

### Using the Floating Action Button

```typescript
// In your component template
<app-add-notes-fab 
  color="primary"
  position="bottom-right"
  tooltipText="Add Notes"
  [preSelectedSeries]="'MANDELA'"
  [preSelectedDenomination]="100">
</app-add-notes-fab>
```

### Pre-selecting Series and Denomination

```typescript
// Open modal with pre-selected series
this.addNotesModalService.openAddNotesModalForSeries('MANDELA')
  .subscribe(result => {
    if (result) {
      this.handleResult(result);
    }
  });

// Open modal with pre-selected series and denomination
this.addNotesModalService.openAddNotesModalForSeriesAndDenomination('MANDELA', 100)
  .subscribe(result => {
    if (result) {
      this.handleResult(result);
    }
  });
```

## Component Structure

```
add-notes-modal/
├── add-notes-modal.component.ts          # Main modal component
├── add-notes-modal.component.html        # Modal template
├── add-notes-modal.component.scss        # Modal styles
├── add-notes-modal.service.ts            # Service for opening modals
├── add-notes-fab.component.ts            # Floating action button
├── add-notes-modal-example.component.ts  # Usage examples
├── index.ts                              # Exports
└── README.md                             # This file
```

## Data Interfaces

### AddNotesDialogData
```typescript
interface AddNotesDialogData {
  title?: string;                    // Custom modal title
  preSelectedSeries?: string;        // Pre-select a series
  preSelectedDenomination?: number;  // Pre-select a denomination
}
```

### AddNotesResult
```typescript
interface AddNotesResult {
  series: string;           // Series ID (e.g., 'MANDELA')
  seriesName: string;       // Series display name
  denomination: number;     // Note denomination
  quantity: number;         // Number of notes
  transactionType: TransactionType; // 'Addition' or 'Subtraction'
  notes?: string;          // Optional notes
  cashTypeId: number;      // Database cash type ID
}
```

## Available Note Series

- **Mandela Series**: R10, R20, R50, R100, R200
- **Big 5 Series**: R10, R20, R50, R100, R200
- **Commemorative Series**: R10, R20, R50, R100, R200
- **V6 Series**: R10, R20, R50, R100, R200

## Floating Action Button Options

### Props
- `color`: 'primary' | 'accent' | 'warn' (default: 'primary')
- `icon`: Material icon name (default: 'note_add')
- `tooltipText`: Tooltip text (default: 'Add Notes')
- `position`: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' (default: 'bottom-right')
- `preSelectedSeries`: Pre-select a series
- `preSelectedDenomination`: Pre-select a denomination

## Customization

The modal can be customized by:
- Passing different `AddNotesDialogData` configurations
- Modifying the SCSS variables for colors and spacing
- Extending the component for additional fields
- Customizing the series and denomination options

## Integration Example

```typescript
import { Component } from '@angular/core';
import { AddNotesModalService, AddNotesResult } from '@shared/components';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-inventory',
  template: `
    <app-add-notes-fab 
      position="bottom-right"
      tooltipText="Add Notes to Inventory">
    </app-add-notes-fab>
  `
})
export class InventoryComponent {
  constructor(
    private addNotesModalService: AddNotesModalService,
    private snackBar: MatSnackBar
  ) {}

  addNotesForSeries(series: string) {
    this.addNotesModalService.openAddNotesModalForSeries(series)
      .subscribe(result => {
        if (result) {
          this.updateInventory(result);
        }
      });
  }

  private updateInventory(result: AddNotesResult) {
    // Call your inventory service to update the notes
    console.log('Updating inventory:', result);
    
    this.snackBar.open(
      `${result.transactionType} ${result.quantity} x R${result.denomination} ${result.seriesName} notes`,
      'Close',
      { duration: 3000 }
    );
  }
}
```
