import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { AddNotesModalComponent, AddNotesDialogData, AddNotesResult } from './add-notes-modal.component';

@Injectable({
  providedIn: 'root'
})
export class AddNotesModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Add Notes modal
   * @param data Configuration data for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddNotesModal(data?: AddNotesDialogData): Observable<AddNotesResult | undefined> {
    const dialogRef: MatDialogRef<AddNotesModalComponent> = this.dialog.open(
      AddNotesModalComponent,
      {
        width: '600px',
        maxWidth: '95vw',
        maxHeight: '90vh',
        disableClose: false,
        autoFocus: true,
        restoreFocus: true,
        data: data || {},
        panelClass: 'add-notes-modal-panel'
      }
    );

    return dialogRef.afterClosed();
  }

  /**
   * Opens the Add Notes modal with a pre-selected series
   * @param series The series to pre-select
   * @param title Optional custom title for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddNotesModalForSeries(
    series: string, 
    title?: string
  ): Observable<AddNotesResult | undefined> {
    return this.openAddNotesModal({
      title: title || `Add Notes - ${series}`,
      preSelectedSeries: series
    });
  }

  /**
   * Opens the Add Notes modal with a pre-selected series and denomination
   * @param series The series to pre-select
   * @param denomination The denomination to pre-select
   * @param title Optional custom title for the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openAddNotesModalForSeriesAndDenomination(
    series: string,
    denomination: number,
    title?: string
  ): Observable<AddNotesResult | undefined> {
    return this.openAddNotesModal({
      title: title || `Add Notes - ${series} R${denomination}`,
      preSelectedSeries: series,
      preSelectedDenomination: denomination
    });
  }

  /**
   * Opens a quick add notes modal with minimal configuration
   * @returns Observable that emits the result when the modal is closed
   */
  openQuickAddNotesModal(): Observable<AddNotesResult | undefined> {
    return this.openAddNotesModal({
      title: 'Quick Add Notes'
    });
  }

  /**
   * Checks if any Add Notes modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof AddNotesModalComponent
    );
  }

  /**
   * Closes all open Add Notes modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof AddNotesModalComponent)
      .forEach(dialog => dialog.close());
  }
}
